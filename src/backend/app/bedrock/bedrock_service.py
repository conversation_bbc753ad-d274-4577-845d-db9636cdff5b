"""
Bedrock + MCP Agent Service

This service implements the Bedrock + MCP agent architecture as described in the README.md.
It uses Amazon Bedrock (Claude 3) as the reasoning engine and MCP servers as the tool execution layer.

Architecture:
User Input -> Agent Orchestrator -> Bedrock Model -> Tool Call -> MCP Server -> Result -> Bedrock -> Final Answer
"""

import json
import logging
import asyncio
import os
from typing import Dict, List, Optional, Any, AsyncGenerator, Union
from dataclasses import dataclass
from enum import Enum
import boto3
import httpx
from dotenv import load_dotenv

# Load environment variables from .env file in bedrock directory
load_dotenv(os.path.join(os.path.dirname(__file__), '.env'))

logger = logging.getLogger(__name__)


class ToolCallStatus(Enum):
    """Status of tool call execution"""
    PENDING = "pending"
    RUNNING = "running" 
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class ToolCall:
    """Represents a tool call from Bedrock"""
    id: str
    name: str
    arguments: Dict[str, Any]
    status: ToolCallStatus = ToolCallStatus.PENDING
    result: Optional[str] = None
    error: Optional[str] = None


@dataclass
class MCPServerConfig:
    """Configuration for an MCP server"""
    name: str
    url: str
    type: str = "streamable-http"
    enabled: bool = True
    tools: Optional[List[Dict[str, Any]]] = None


class BedrockMCPOrchestrator:
    """
    Bedrock + MCP Agent Orchestrator
    
    This class implements the exact architecture from the README:
    1. Takes user input
    2. Calls Bedrock with available tools
    3. Executes tool calls via MCP servers
    4. Feeds results back to Bedrock for final answer
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize the Bedrock MCP orchestrator.
        
        Args:
            config_file: Path to MCP configuration file (defaults to .stitch/mcp.json)
        """
        self.config_file = config_file or ".stitch/mcp.json"
        
        # Bedrock configuration
        self.bedrock_model_id = os.getenv("BEDROCK_MODEL_ID", "anthropic.claude-3-sonnet-20240229-v1:0")
        self.aws_region = os.getenv("AWS_REGION", "us-east-1")
        
        # Initialize Bedrock client
        self.bedrock_client = self._init_bedrock_client()
        
        # MCP servers and tools
        self.mcp_servers: Dict[str, MCPServerConfig] = {}
        self.available_tools: List[Dict[str, Any]] = []
        
        # HTTP client for MCP communication
        self.http_client: Optional[httpx.AsyncClient] = None
        
    def _init_bedrock_client(self):
        """Initialize AWS Bedrock client with credentials from environment"""
        session_kwargs = {"region_name": self.aws_region}
        
        # Add credentials if provided in environment
        aws_access_key = os.getenv("AWS_ACCESS_KEY_ID")
        if aws_access_key:
            session_kwargs["aws_access_key_id"] = aws_access_key
        aws_secret_key = os.getenv("AWS_SECRET_ACCESS_KEY")
        if aws_secret_key:
            session_kwargs["aws_secret_access_key"] = aws_secret_key
        aws_session_token = os.getenv("AWS_SESSION_TOKEN")
        if aws_session_token:
            session_kwargs["aws_session_token"] = aws_session_token
            
        return boto3.client("bedrock-runtime", **session_kwargs)
    
    async def initialize(self) -> bool:
        """Initialize the orchestrator by loading MCP servers and discovering tools"""
        try:
            # Initialize HTTP client
            self.http_client = httpx.AsyncClient(timeout=30.0)
            
            # Load MCP server configurations
            if not await self._load_mcp_config():
                logger.warning("No MCP configuration found, starting with empty tools")
            
            # Discover tools from all configured MCP servers
            await self._discover_all_tools()
            
            logger.info(f"Bedrock MCP Orchestrator initialized with {len(self.mcp_servers)} servers and {len(self.available_tools)} tools")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Bedrock MCP Orchestrator: {str(e)}")
            return False
    
    async def _load_mcp_config(self) -> bool:
        """Load MCP server configurations from JSON file"""
        try:
            if not os.path.exists(self.config_file):
                logger.warning(f"MCP config file not found: {self.config_file}")
                return False
                
            with open(self.config_file, 'r') as f:
                config_data = json.load(f)
            
            mcp_servers = config_data.get('mcpServers', {})
            
            for server_name, server_config in mcp_servers.items():
                if server_config.get('disabled', False):
                    logger.info(f"Skipping disabled MCP server: {server_name}")
                    continue
                
                self.mcp_servers[server_name] = MCPServerConfig(
                    name=server_name,
                    url=server_config.get('url', ''),
                    type=server_config.get('type', 'streamable-http'),
                    enabled=not server_config.get('disabled', False)
                )
                
                logger.info(f"Loaded MCP server: {server_name} at {server_config.get('url')}")
            
            return len(self.mcp_servers) > 0
            
        except Exception as e:
            logger.error(f"Error loading MCP config: {str(e)}")
            return False
    
    async def _discover_all_tools(self):
        """Discover tools from all configured MCP servers"""
        self.available_tools.clear()
        
        for server_name, server_config in self.mcp_servers.items():
            try:
                tools = await self._discover_server_tools(server_config)
                server_config.tools = tools
                
                # Add tools to available tools list with server prefix
                for tool in tools:
                    tool_with_server = tool.copy()
                    tool_with_server['name'] = f"{server_name}:{tool['name']}"
                    tool_with_server['server_name'] = server_name
                    self.available_tools.append(tool_with_server)
                
                logger.info(f"Discovered {len(tools)} tools from {server_name}")
                
            except Exception as e:
                logger.error(f"Failed to discover tools from {server_name}: {str(e)}")
    
    async def _discover_server_tools(self, server_config: MCPServerConfig) -> List[Dict[str, Any]]:
        """Discover tools from a specific MCP server"""
        if not self.http_client:
            return []
        
        try:
            if server_config.type == "streamable-http":
                # Try different MCP endpoint patterns
                base_url = server_config.url.rstrip('/')
                possible_urls = [
                    f"{base_url}/mcp/",
                    f"{base_url}/mcp",
                    f"{base_url}/"
                ]
                
                for url in possible_urls:
                    try:
                        logger.info(f"Trying MCP endpoint: {url}")
                        
                        # Initialize MCP session
                        init_payload = {
                            "method": "initialize",
                            "params": {
                                "protocolVersion": "2024-11-05",
                                "capabilities": {}
                            }
                        }
                        
                        response = await self.http_client.post(url, json=init_payload, timeout=10.0)
                        logger.info(f"Initialize response status: {response.status_code}")
                        
                        if response.status_code == 200:
                            # List tools
                            tools_payload = {"method": "listTools", "params": {}}
                            tools_response = await self.http_client.post(url, json=tools_payload, timeout=10.0)
                            
                            logger.info(f"List tools response status: {tools_response.status_code}")
                            
                            if tools_response.status_code == 200:
                                tools_data = tools_response.json()
                                logger.info(f"Tools data received: {tools_data}")
                                
                                tools = []
                                tools_list = tools_data.get("result", {}).get("tools", [])
                                
                                for tool in tools_list:
                                    # Convert MCP tool format to Bedrock tool format
                                    bedrock_tool = {
                                        "name": tool["name"],
                                        "description": tool.get("description", f"Tool: {tool['name']}"),
                                        "input_schema": tool.get("inputSchema", {
                                            "type": "object",
                                            "properties": {},
                                            "required": []
                                        })
                                    }
                                    tools.append(bedrock_tool)
                                
                                logger.info(f"Successfully discovered {len(tools)} tools from {server_config.name}")
                                return tools
                            else:
                                logger.warning(f"Failed to list tools from {url}: {tools_response.status_code}")
                        else:
                            logger.warning(f"Failed to initialize MCP session at {url}: {response.status_code}")
                            
                    except Exception as e:
                        logger.warning(f"Failed to connect to {url}: {str(e)}")
                        continue
                
                logger.error(f"All MCP endpoints failed for {server_config.name}")
                        
        except Exception as e:
            logger.error(f"Error discovering tools from {server_config.name}: {str(e)}")
        
        return []
    
    async def query(self, user_message: str, conversation_history: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        Process a user query using the Bedrock + MCP orchestration pattern.
        
        Args:
            user_message: The user's natural language query
            conversation_history: Optional conversation history
            
        Returns:
            Dict containing the response, tool calls, and metadata
        """
        try:
            # Step 1: Prepare messages for Bedrock
            messages = self._prepare_messages(user_message, conversation_history)
            
            # Step 2: Call Bedrock with available tools
            logger.info(f"Calling Bedrock with {len(self.available_tools)} available tools")
            bedrock_response = await self._call_bedrock(messages, self.available_tools)
            
            # Step 3: Check if model made tool calls
            tool_calls = self._extract_tool_calls(bedrock_response)
            
            if tool_calls:
                logger.info(f"Bedrock requested {len(tool_calls)} tool calls")
                
                # Step 4: Execute tool calls via MCP servers
                tool_results = await self._execute_tool_calls(tool_calls)
                
                # Step 5: Send results back to Bedrock for final answer
                updated_messages = self._add_tool_results_to_messages(messages, bedrock_response, tool_results)
                final_response = await self._call_bedrock(updated_messages)
                
                return {
                    "answer": self._extract_text_content(final_response),
                    "tool_calls": [
                        {
                            "name": tc.name,
                            "arguments": tc.arguments,
                            "result": tc.result,
                            "status": tc.status.value,
                            "error": tc.error
                        } for tc in tool_calls
                    ],
                    "intermediate_steps": [
                        f"Called tool: {tc.name}" for tc in tool_calls
                    ],
                    "conversation_history": updated_messages
                }
            else:
                # No tool calls needed, return direct response
                return {
                    "answer": self._extract_text_content(bedrock_response),
                    "tool_calls": [],
                    "intermediate_steps": ["Direct response from Bedrock"],
                    "conversation_history": messages + [{"role": "assistant", "content": bedrock_response.get("content", [])}]
                }
                
        except Exception as e:
            logger.error(f"Error processing query: {str(e)}")
            return {
                "answer": f"Error processing your request: {str(e)}",
                "tool_calls": [],
                "error": str(e)
            }
    
    async def stream_query(self, user_message: str, conversation_history: Optional[List[Dict[str, Any]]] = None) -> AsyncGenerator[str, None]:
        """
        Process a user query with streaming responses.
        
        Args:
            user_message: The user's natural language query
            conversation_history: Optional conversation history
            
        Yields:
            JSON strings with streaming updates
        """
        try:
            yield json.dumps({"type": "status", "message": "Starting query processing..."})
            
            # Step 1: Prepare messages
            messages = self._prepare_messages(user_message, conversation_history)
            
            yield json.dumps({"type": "status", "message": f"Calling Bedrock with {len(self.available_tools)} available tools..."})
            
            # Step 2: Call Bedrock
            bedrock_response = await self._call_bedrock(messages, self.available_tools)
            
            # Step 3: Check for tool calls
            tool_calls = self._extract_tool_calls(bedrock_response)
            
            if tool_calls:
                yield json.dumps({
                    "type": "tool_calls_detected", 
                    "count": len(tool_calls),
                    "tools": [tc.name for tc in tool_calls]
                })
                
                # Step 4: Execute tool calls with streaming updates
                tool_results = []
                for i, tool_call in enumerate(tool_calls):
                    yield json.dumps({
                        "type": "tool_executing",
                        "tool_name": tool_call.name,
                        "arguments": tool_call.arguments,
                        "step": i + 1,
                        "total": len(tool_calls)
                    })
                    
                    result = await self._execute_single_tool_call(tool_call)
                    tool_results.append(result)
                    
                    yield json.dumps({
                        "type": "tool_completed",
                        "tool_name": tool_call.name,
                        "status": tool_call.status.value,
                        "result_preview": (tool_call.result or "")[:200] + ("..." if len(tool_call.result or "") > 200 else "")
                    })
                
                yield json.dumps({"type": "status", "message": "Getting final response from Bedrock..."})
                
                # Step 5: Get final response
                updated_messages = self._add_tool_results_to_messages(messages, bedrock_response, tool_results)
                final_response = await self._call_bedrock(updated_messages)
                
                yield json.dumps({
                    "type": "final_answer",
                    "answer": self._extract_text_content(final_response),
                    "tool_calls": [
                        {
                            "name": tc.name,
                            "arguments": tc.arguments,
                            "result": tc.result,
                            "status": tc.status.value
                        } for tc in tool_calls
                    ]
                })
            else:
                # Direct response
                yield json.dumps({
                    "type": "final_answer",
                    "answer": self._extract_text_content(bedrock_response),
                    "tool_calls": []
                })
                
            yield json.dumps({"type": "complete", "message": "Query processing completed"})
            
        except Exception as e:
            logger.error(f"Error in stream query: {str(e)}")
            yield json.dumps({
                "type": "error",
                "error": str(e),
                "message": f"Error processing query: {str(e)}"
            })
    
    def _prepare_messages(self, user_message: str, conversation_history: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """Prepare messages in Bedrock format"""
        messages = []
        
        # Add conversation history if provided
        if conversation_history:
            messages.extend(conversation_history)
        
        # Add current user message
        messages.append({"role": "user", "content": user_message})
        
        return messages
    
    async def _call_bedrock(self, messages: List[Dict[str, Any]], tools: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        Call Bedrock model with messages and optional tools.
        This follows the exact pattern from the README example.
        """
        body = {
            "anthropic_version": "bedrock-2023-05-31",
            "max_tokens": 4096,
            "messages": messages,
        }
        
        if tools:
            body["tools"] = tools
        
        response = self.bedrock_client.invoke_model(
            modelId=self.bedrock_model_id,
            body=json.dumps(body),
        )
        
        return json.loads(response["body"].read())
    
    def _extract_tool_calls(self, bedrock_response: Dict[str, Any]) -> List[ToolCall]:
        """Extract tool calls from Bedrock response"""
        tool_calls = []
        content = bedrock_response.get("content", [])
        
        for item in content:
            if item.get("type") == "tool_use":
                tool_call = ToolCall(
                    id=item.get("id", f"tool_{len(tool_calls)}"),
                    name=item["name"],
                    arguments=item.get("input", {})
                )
                tool_calls.append(tool_call)
        
        return tool_calls
    
    async def _execute_tool_calls(self, tool_calls: List[ToolCall]) -> List[ToolCall]:
        """Execute all tool calls"""
        results = []
        
        for tool_call in tool_calls:
            result = await self._execute_single_tool_call(tool_call)
            results.append(result)
        
        return results
    
    async def _execute_single_tool_call(self, tool_call: ToolCall) -> ToolCall:
        """
        Execute a single tool call via MCP server.
        This follows the exact pattern from the README: call_mcp_tool(tool_name, args)
        """
        try:
            tool_call.status = ToolCallStatus.RUNNING
            logger.info(f"🔧 Calling MCP tool {tool_call.name} with args {tool_call.arguments}")
            
            # Find the server for this tool
            server_name = tool_call.name.split(":", 1)[0] if ":" in tool_call.name else None
            clean_tool_name = tool_call.name.split(":", 1)[-1]
            
            if not server_name or server_name not in self.mcp_servers:
                tool_call.status = ToolCallStatus.FAILED
                tool_call.error = f"Unknown server for tool: {tool_call.name}"
                return tool_call
            
            server_config = self.mcp_servers[server_name]
            
            # Call MCP tool (following README pattern)
            result = await self._call_mcp_tool(server_config, clean_tool_name, tool_call.arguments)
            
            tool_call.result = json.dumps(result) if isinstance(result, (dict, list)) else str(result)
            tool_call.status = ToolCallStatus.COMPLETED
            
            logger.info(f"✅ Tool {tool_call.name} completed successfully")
            
        except Exception as e:
            tool_call.status = ToolCallStatus.FAILED
            tool_call.error = str(e)
            logger.error(f"❌ Tool {tool_call.name} failed: {str(e)}")
        
        return tool_call
    
    async def _call_mcp_tool(self, server_config: MCPServerConfig, tool_name: str, args: Dict[str, Any]) -> Any:
        """
        Call MCP server tool over HTTP.
        This implements the exact pattern from the README example.
        """
        if not self.http_client:
            raise Exception("HTTP client not initialized")
        
        # Construct MCP payload (following README pattern)
        payload = {
            "method": "callTool",
            "params": {"name": tool_name, "arguments": args},
        }
        
        # Construct URL
        mcp_server_url = server_config.url.rstrip('/')
        if not mcp_server_url.endswith('/mcp'):
            mcp_server_url += '/mcp'
        
        # Make HTTP call to MCP server
        response = await self.http_client.post(mcp_server_url, json=payload, timeout=30.0)
        response.raise_for_status()
        
        return response.json()
    
    def _add_tool_results_to_messages(
        self,
        messages: List[Dict[str, Any]],
        bedrock_response: Dict[str, Any],
        tool_results: List[ToolCall]
    ) -> List[Dict[str, Any]]:
        """Add tool results to conversation for final Bedrock call"""
        # Add assistant's response with tool calls
        messages.append({"role": "assistant", "content": bedrock_response.get("content", [])})
        
        # Add tool results as user messages (following Bedrock format)
        for tool_call in tool_results:
            messages.append({
                "role": "user",
                "content": [
                    {
                        "type": "tool_result",
                        "tool_use_id": tool_call.id,
                        "content": tool_call.result or tool_call.error or "No result"
                    }
                ]
            })
        
        return messages
    
    def _extract_text_content(self, bedrock_response: Dict[str, Any]) -> str:
        """Extract text content from Bedrock response"""
        content = bedrock_response.get("content", [])
        
        for item in content:
            if item.get("type") == "text":
                return item.get("text", "")
        
        return "No response generated"
    
    async def cleanup(self):
        """Clean up resources"""
        if self.http_client:
            await self.http_client.aclose()
    
    def get_available_tools(self) -> List[Dict[str, Any]]:
        """Get list of available tools"""
        return self.available_tools.copy()
    
    def get_mcp_servers(self) -> Dict[str, MCPServerConfig]:
        """Get configured MCP servers"""
        return self.mcp_servers.copy()
    
    def add_mcp_server(self, name: str, url: str, server_type: str = "streamable-http") -> bool:
        """
        Add a new MCP server configuration.
        
        Args:
            name: Server name
            url: Server URL
            server_type: Server type (default: streamable-http)
            
        Returns:
            True if server was added successfully
        """
        try:
            self.mcp_servers[name] = MCPServerConfig(
                name=name,
                url=url,
                type=server_type,
                enabled=True
            )
            logger.info(f"Added MCP server: {name} at {url}")
            return True
        except Exception as e:
            logger.error(f"Failed to add MCP server {name}: {str(e)}")
            return False
    
    async def reload_tools(self):
        """Reload tools from all configured MCP servers"""
        await self._discover_all_tools()
        logger.info(f"Reloaded tools. Now have {len(self.available_tools)} tools available")
